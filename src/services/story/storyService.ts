import httpBase, { RequestCallbacks } from '../http/httpBase'

// Story generation interfaces
export interface StoryGenerationResponse {
  story_id: string
  status: string
}

export interface StoryStep {
  stage: number
  script: string
  image: string
  audio_url?: string
  media: {
    object_name: string
    folder: string
    media_type: string
    bucket_name: string
    content_type: string
    size_bytes: number
    url: string
  }
}

// Interface for filtered stories list (different structure)
export interface StoryListItem {
  story_id: string
  first_step_script: string
  first_step_image: string
  media_url?: string
  audio_url?: string
  total_steps: number
  completed_steps: number
  status: string
  created_at: string
  updated_at: string
}

// Interface for individual story with stage details
export interface StoryData {
  step: StoryStep
  story_id: string
  total_steps: number
  completed_steps: number
  status: string
  created_at: string
  updated_at: string
}

export interface StoryResponse {
  data: StoryData[]
  meta: {
    page: number
    limit: number
    total: number
    total_pages: number
  }
}

export interface StoryListResponse {
  data: StoryListItem[]
  meta: {
    page: number
    limit: number
    total: number
    total_pages: number
  }
}

export interface StoryIdsResponse {
  story_ids: string[]
  total_count: number
}

// New interface for single story response (new API format)
export interface SingleStoryResponse {
  script: string
  metadata: {
    object_name: string
    bucket_name: string
    object_path: string
    file_name: string
    url: string
    content_type: string
    size_bytes: number
    user_id: string
    folder: string
    session_id: string | null
    created_at: string
    file_extension: string
    _image_ready: boolean
    _priority: string
  }
  created_at: string
  updated_at: string
  id: string
}

class StoryService {
  // Generate story from audio file
  async generateStory(
    audioFile: File,
    callbacks?: RequestCallbacks<StoryGenerationResponse>
  ): Promise<StoryGenerationResponse> {
    const formData = new FormData()
    formData.append('audio_file', audioFile)

    const response = await httpBase.request<StoryGenerationResponse>(
      {
        method: 'POST',
        url: '/socket/story/generate',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      },
      callbacks
    )

    return response.data
  }

  // Get story by ID and stage
  async getStory(
    storyId: string,
    stage: number = 1,
    callbacks?: RequestCallbacks<StoryResponse>
  ): Promise<StoryResponse> {
    const response = await httpBase.get<StoryResponse>(
      `/management/story/${storyId}?stage=${stage}`,
      {},
      callbacks
    )

    return response.data
  }

  // Get story by ID with specific fields
  async getStoryWithFields(
    storyId: string,
    fields: string[] = ['steps', 'total_steps', 'completed_steps', 'status', 'created_at', 'updated_at', 'metadata'],
    callbacks?: RequestCallbacks<any>
  ): Promise<any> {
    const params = new URLSearchParams()
    fields.forEach(field => params.append('fields', field))

    const response = await httpBase.get<any>(
      `/management/story/${storyId}?${params.toString()}`,
      {},
      callbacks
    )

    return response.data
  }

  // Get all stages of a story
  async getAllStoryStages(
    storyId: string,
    callbacks?: RequestCallbacks<StoryResponse>
  ): Promise<StoryResponse> {
    const response = await httpBase.get<StoryResponse>(
      `/management/story/${storyId}`,
      {},
      callbacks
    )

    return response.data
  }

  // Get filtered stories
  async getFilteredStories(
    page: number = 1,
    limit: number = 10,
    callbacks?: RequestCallbacks<StoryListResponse>
  ): Promise<StoryListResponse> {
    const response = await httpBase.get<StoryListResponse>(
      `/management/story/all/filtered?page=${page}&limit=${limit}`,
      {},
      callbacks
    )

    return response.data
  }

  // Get single story by ID (new API format)
  async getSingleStory(
    storyId: string,
    callbacks?: RequestCallbacks<SingleStoryResponse>
  ): Promise<SingleStoryResponse> {
    const response = await httpBase.get<SingleStoryResponse>(
      `/management/story/${storyId}?stage=1`,
      {},
      callbacks
    )

    return response.data
  }
}

// Export singleton instance
export const storyService = new StoryService()
export default storyService

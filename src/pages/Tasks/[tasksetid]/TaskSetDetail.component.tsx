import React from 'react'
import { motion } from 'framer-motion'
import {
  RefreshC<PERSON>,
  Headphones,
  Clock,
  ChevronRight,
  AlertCircle,
  FileText,
  Play,
  ArrowLeft,
  RotateCcw
} from 'lucide-react'
import { Link } from 'react-router-dom'
import MainLayout from '../../../components/layout/MainLayout'
import SimpleAudioPlayer from '../../../components/audio/SimpleAudioPlayer.tsx'
import { TaskSetDetailSkeleton, TaskItemsSkeleton } from '../../../components/ui/Skeleton'
import TaskSetScoreDisplay from '../../../components/ui/TaskSetScoreDisplay'
import TaskItemOptionsModal from '../../../components/task/TaskItemOptionsModal'
import { cn } from '../../../utils/cn'

interface TaskSetDetailComponentProps {
  taskSetId: string
  taskSet: any | null
  taskSetScore: any | null
  taskItems?: any[] // Optional array of task items for displaying question text
  loading: boolean
  loadingScore?: boolean
  error: string | null
  onTaskItemClick: (taskItemId: string, taskIndex: number) => void
  onViewOutput: () => void
  onRefresh: () => void
  onRetryTasks?: () => void
  showTaskOptionsModal: boolean
  selectedTaskId: string | null
  selectedTaskIndex: number
  onTaskOptionsModalClose: () => void
  onRetryTask: () => void
  onViewQuiz: () => void
  onViewStory: () => void
}

/**
 * TaskSetDetail Component - Pure UI component for task set detail page
 */
const TaskSetDetailComponent: React.FC<TaskSetDetailComponentProps> = ({
  taskSet,
  taskSetScore,
  taskItems,
  loading,
  loadingScore = false,
  error,
  onTaskItemClick,
  onViewOutput,
  onRefresh,
  onRetryTasks,
  showTaskOptionsModal,
  selectedTaskId,
  selectedTaskIndex,
  onTaskOptionsModalClose,
  onRetryTask,
  onViewQuiz,
  onViewStory
}) => {
  // Helper function to get display title from input_content
  const getTaskSetTitle = () => {
    if (!taskSet?.input_content) return 'Task Set'

    // If input_content is a string, return it
    if (typeof taskSet.input_content === 'string') {
      return taskSet.input_content
    }

    // If input_content is an object, extract meaningful title
    if (typeof taskSet.input_content === 'object') {
      return taskSet.input_content.file_name ||
             taskSet.input_content.object_name ||
             'Audio Task Set'
    }

    return 'Task Set'
  }

  // Helper function to get audio URL from input_content
  const getAudioUrl = () => {
    if (!taskSet?.input_content || typeof taskSet.input_content !== 'object') {
      return null
    }
    return taskSet.input_content.url || null
  }

  // Helper function to check if this is an audio task set
  const isAudioTaskSet = () => {
    return taskSet?.input_type === 'audio' && getAudioUrl()
  }
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    },
    hover: {
      scale: 1.02,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  }

  const calculateProgress = () => {
    // Since we don't have individual task status, show 0% progress
    // This will be updated when individual tasks are fetched
    return 0
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    return date.toLocaleDateString()
  }

  return (
    <MainLayout
      title={taskSet ? getTaskSetTitle() : 'Task Set Details'}
      description="View and complete task items in this set"
    >
      <div className="space-y-6">
        {/* Back Navigation */}
        <div className="flex items-center gap-3">
          <Link 
            to="/tasks" 
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm">Back to Tasks</span>
          </Link>
        </div>

        {/* Error state */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-destructive/10 border border-destructive/20 rounded-xl p-6"
          >
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <div>
                <h3 className="font-medium text-destructive">Error</h3>
                <p className="text-sm text-destructive/80">{error}</p>
              </div>
              <button
                onClick={onRefresh}
                className="ml-auto p-2 hover:bg-destructive/10 rounded-lg transition-colors"
              >
                <RefreshCw className="h-4 w-4 text-destructive" />
              </button>
            </div>
          </motion.div>
        )}

        {/* Loading state */}
        {loading ? (
          <div className="space-y-6">
            <TaskSetDetailSkeleton />
            <TaskItemsSkeleton count={3} />
          </div>
        ) : taskSet ? (
          <>
            {/* Task Set Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-card border border-border rounded-xl p-6 shadow-sm"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "p-3 rounded-lg",
                    taskSet.input_type === 'audio'
                      ? "bg-blue-100 dark:bg-blue-900/20"
                      : "bg-purple-100 dark:bg-purple-900/20"
                  )}>
                    {taskSet.input_type === 'audio' ? (
                      <Headphones className={cn(
                        "h-5 w-5",
                        "text-blue-600 dark:text-blue-400"
                      )} />
                    ) : (
                      <FileText className={cn(
                        "h-5 w-5",
                        "text-purple-600 dark:text-purple-400"
                      )} />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>Created {formatTimeAgo(taskSet.created_at)}</span>
                      <span>•</span>
                      <span>{taskSet.tasks?.length || 0} task{(taskSet.tasks?.length || 0) !== 1 ? 's' : ''}</span>
                      <span>•</span>
                      <span>{calculateProgress()}% complete</span>
                    </div>

                  </div>
                </div>
                <button
                  onClick={onRefresh}
                  className="p-2 hover:bg-accent rounded-lg transition-colors"
                  title="Refresh"
                >
                  <RefreshCw className="h-4 w-4 text-muted-foreground" />
                </button>
              </div>

              {/* Simple Audio Player for audio task sets */}
              {isAudioTaskSet() && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="mb-6"
                >
                  <SimpleAudioPlayer
                    src={getAudioUrl()!}
                    title={getTaskSetTitle()}
                  />
                </motion.div>
              )}

              {/* Task Set Score Display */}
              <TaskSetScoreDisplay
                score={taskSetScore}
                loading={loadingScore}
                className="mb-6"
              />

              {/* Action Buttons */}
              {taskSet.tasks && taskSet.tasks.length > 0 && (
                <div className="flex justify-center gap-3">
                  <button
                    onClick={onViewOutput}
                    className="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors flex items-center gap-2"
                  >
                    <Play className="h-4 w-4" />
                    View Output
                  </button>
                  {onRetryTasks && (
                    <button
                      onClick={onRetryTasks}
                      className="px-6 py-3 bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors flex items-center gap-2"
                    >
                      <RotateCcw className="h-4 w-4" />
                      Retry Tasks
                    </button>
                  )}
                </div>
              )}
            </motion.div>

            {/* Task Items List */}
            {!taskSet.tasks || taskSet.tasks.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12 px-6 bg-card border border-border rounded-xl"
              >
                <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-muted mb-4">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-medium mb-2 text-card-foreground">No tasks found</h3>
                <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
                  This task set doesn't have any tasks yet.
                </p>
              </motion.div>
            ) : (
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6"
              >
                {taskSet.tasks.map((taskId: string, index: number) => {
                  // Find corresponding task item for this task ID
                  const taskItem = taskItems?.find(item =>
                    (item._id === taskId) || (item.id === taskId)
                  )

                  // Extract question text
                  const questionText = taskItem
                    ? (typeof taskItem.question === 'string'
                        ? taskItem.question
                        : taskItem.question?.text || `Task ${index + 1}`)
                    : `Task ${index + 1}`

                  return (
                    <motion.div
                      key={taskId}
                      variants={cardVariants}
                      whileHover="hover"
                      className="cursor-pointer"
                      onClick={() => onTaskItemClick(taskId, index)}
                    >
                      <div className="bg-card border border-border rounded-xl p-3 sm:p-4 shadow-sm hover:shadow-md transition-shadow">
                        {/* Header */}
                        <div className="flex items-start justify-between mb-2 sm:mb-3">
                          <div className="flex items-center gap-1.5 sm:gap-2">
                            <div className="w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400">
                              {index + 1}
                            </div>
                            <div className="p-0.5 sm:p-1 rounded-full bg-blue-100 dark:bg-blue-900/20">
                              <Clock className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-blue-600 dark:text-blue-400" />
                            </div>
                          </div>
                          <ChevronRight className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-muted-foreground" />
                        </div>

                        {/* Content */}
                        <h3 className="font-medium text-card-foreground mb-2 line-clamp-2 text-sm sm:text-base">
                          {questionText}
                        </h3>

                        <div className="flex items-center justify-between text-xs sm:text-sm">
                          <span className="text-muted-foreground truncate">
                            {taskItem?.type?.replace('_', ' ') || taskSet.input_type || 'audio'} task
                          </span>
                          <span className="font-medium text-blue-600 ml-2">
                            {taskItem ? `${taskItem.scored || 0}/${taskItem.total_score || 10}` : '0/10'}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </motion.div>
            )}
          </>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-12 px-6 bg-card border border-border rounded-xl"
          >
            <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-muted mb-4">
              <AlertCircle className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-medium mb-2 text-card-foreground">Task set not found</h3>
            <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
              The requested task set could not be found.
            </p>
          </motion.div>
        )}
      </div>

      {/* Task Item Options Modal */}
      {selectedTaskId && (
        <TaskItemOptionsModal
          open={showTaskOptionsModal}
          onOpenChange={onTaskOptionsModalClose}
          taskId={selectedTaskId}
          taskIndex={selectedTaskIndex}
          taskSetId={taskSet?._id || ''}
          onRetryTask={onRetryTask}
          onViewQuiz={onViewQuiz}
          onViewStory={onViewStory}
          hasStory={true} // For now, assume all tasks have stories
        />
      )}
    </MainLayout>
  )
}

export default TaskSetDetailComponent

import React, { useEffect, useState } from 'react'
import { useParams, useNavigate, useSearchParams } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '../../../../../store/hooks'
import { storyService } from '../../../../../services/story/storyService'
import { useStoryCache } from '../../../../../hooks/useStoryCache'
import { markStoryAsVisited, undismissStoryNotification } from '../../../../../store/slices/storySlice'
import StoryItemComponent from './StoryItem.component'

interface StoryItemState {
  loading: boolean
  error: string | null
}

/**
 * StoryItem Container - Handles logic and state for story item page within tasks structure
 */
const StoryItemContainer: React.FC = () => {
  const { tasksetid, storyitemid } = useParams<{ tasksetid: string; storyitemid: string }>()
  const { user, isAuthenticated } = useAppSelector((state) => state.auth)
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()

  // Extract current stage from query parameter
  const currentStage = parseInt(searchParams.get('stage') || '1', 10)

  const [state, setState] = useState<StoryItemState>({
    loading: false,
    error: null
  })

  // Use the existing story cache hook
  const {
    storyData,
    isLoading,
    error: storyError,
    totalStages,
    navigateToStage
  } = useStoryCache(storyitemid || '', currentStage)

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, navigate])

  // Mark story as visited when user navigates to it
  useEffect(() => {
    if (storyitemid) {
      dispatch(markStoryAsVisited(storyitemid))
      dispatch(undismissStoryNotification(storyitemid))
    }
  }, [storyitemid, dispatch])

  // Redirect to stage 1 if no stage specified
  useEffect(() => {
    if (storyitemid && !searchParams.get('stage')) {
      setSearchParams({ stage: '1' }, { replace: true })
    }
  }, [storyitemid, searchParams, setSearchParams])

  const handleStageChange = (newStage: number) => {
    if (!storyitemid || newStage < 1 || newStage > totalStages) return

    setSearchParams({ stage: newStage.toString() })
    navigateToStage(newStage)
  }

  const handlePreviousStage = () => {
    if (currentStage > 1) {
      handleStageChange(currentStage - 1)
    }
  }

  const handleNextStage = () => {
    if (currentStage < totalStages) {
      handleStageChange(currentStage + 1)
    }
  }

  const handleGoBack = () => {
    // Navigate back to the task set detail page
    if (tasksetid) {
      navigate(`/tasks/${tasksetid}`)
    } else {
      navigate('/tasks')
    }
  }

  if (!isAuthenticated || !user) {
    return null
  }

  if (!tasksetid || !storyitemid) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-foreground mb-2">Invalid Story</h2>
          <p className="text-muted-foreground">Missing task set ID or story ID.</p>
        </div>
      </div>
    )
  }

  return (
    <StoryItemComponent
      taskSetId={tasksetid}
      storyId={storyitemid}
      storyData={storyData}
      currentStage={currentStage}
      totalStages={totalStages}
      isLoading={isLoading}
      error={storyError || state.error}
      onStageChange={handleStageChange}
      onPreviousStage={handlePreviousStage}
      onNextStage={handleNextStage}
      onGoBack={handleGoBack}
    />
  )
}

export default StoryItemContainer

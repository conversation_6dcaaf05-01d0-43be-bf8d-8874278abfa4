import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  BookOpen,
  Loader2,
  Play,
  Pause,
  Volume2,
  VolumeX,
  RotateCcw
} from 'lucide-react'
import MainLayout from '../../../../../components/layout/MainLayout'
import { cn } from '../../../../../utils/cn'

interface StoryItemComponentProps {
  taskSetId: string
  storyId: string
  storyData: any | null
  currentStage: number
  totalStages: number
  isLoading: boolean
  error: string | null
  onStageChange: (stage: number) => void
  onPreviousStage: () => void
  onNextStage: () => void
  onGoBack: () => void
}

/**
 * StoryItem Component - Pure UI component for story item page within tasks structure
 */
const StoryItemComponent: React.FC<StoryItemComponentProps> = ({
  taskSetId,
  storyId,
  storyData,
  currentStage,
  totalStages,
  isLoading,
  error,
  onStageChange,
  onPreviousStage,
  onNextStage,
  onGoBack
}) => {
  // Audio player state
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const audioRef = useRef<HTMLAudioElement>(null)

  // Audio player handlers
  const handlePlayPause = () => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play()
    }
    setIsPlaying(!isPlaying)
  }

  const handleMuteToggle = () => {
    if (!audioRef.current) return
    audioRef.current.muted = !isMuted
    setIsMuted(!isMuted)
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return
    const newTime = parseFloat(e.target.value)
    audioRef.current.currentTime = newTime
    setCurrentTime(newTime)
  }

  const handleRestart = () => {
    if (!audioRef.current) return
    audioRef.current.currentTime = 0
    setCurrentTime(0)
  }

  // Audio event handlers
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    const updateTime = () => setCurrentTime(audio.currentTime)
    const updateDuration = () => setDuration(audio.duration)
    const handleEnded = () => setIsPlaying(false)

    audio.addEventListener('timeupdate', updateTime)
    audio.addEventListener('loadedmetadata', updateDuration)
    audio.addEventListener('ended', handleEnded)

    return () => {
      audio.removeEventListener('timeupdate', updateTime)
      audio.removeEventListener('loadedmetadata', updateDuration)
      audio.removeEventListener('ended', handleEnded)
    }
  }, [storyData])

  // Format time for display
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const getStoryTitle = () => {
    return `Story - Stage ${currentStage}`
  }

  return (
    <MainLayout
      title={getStoryTitle()}
      description="Interactive story experience"
    >
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header with navigation */}
        <div className="flex items-center justify-between">
          <button
            onClick={onGoBack}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm">Back to Task Set</span>
          </button>

          {/* Stage navigation */}
          {totalStages > 1 && (
            <div className="flex items-center gap-4">
              <button
                onClick={onPreviousStage}
                disabled={currentStage <= 1}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  currentStage <= 1
                    ? "text-muted-foreground cursor-not-allowed"
                    : "text-foreground hover:bg-accent"
                )}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>

              <div className="flex items-center gap-2">
                <BookOpen className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium">
                  Stage {currentStage} of {totalStages}
                </span>
              </div>

              <button
                onClick={onNextStage}
                disabled={currentStage >= totalStages}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  currentStage >= totalStages
                    ? "text-muted-foreground cursor-not-allowed"
                    : "text-foreground hover:bg-accent"
                )}
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>

        {/* Loading state */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center justify-center py-12"
          >
            <div className="flex items-center gap-3">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
              <span className="text-lg text-muted-foreground">Loading story...</span>
            </div>
          </motion.div>
        )}

        {/* Error state */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-destructive/10 border border-destructive/20 rounded-xl p-6"
          >
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <div>
                <h3 className="font-medium text-destructive">Error Loading Story</h3>
                <p className="text-sm text-destructive/80">{error}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Story content */}
        {!isLoading && !error && storyData && (
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStage}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              className="bg-card border border-border rounded-xl overflow-hidden shadow-lg"
            >
              {storyData.step?.image ? (
                // Image + text layout
                <div className="grid grid-cols-1 lg:grid-cols-2 min-h-[600px]">
                  <div className="relative bg-slate-100 flex items-center justify-center p-6">
                    <motion.img
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.6, ease: "easeOut" }}
                      src={storyData.step.image}
                      alt={`Story stage ${currentStage}`}
                      className="w-full h-full object-contain rounded-xl max-h-[500px]"
                    />
                  </div>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.5, ease: "easeOut" }}
                    className="shrink-0 bg-card p-8 flex flex-col justify-center"
                  >
                    <h2 className="text-3xl font-bold text-card-foreground mb-6">
                      Chapter {currentStage}
                    </h2>
                    <p className="text-card-foreground leading-relaxed text-lg mb-6">
                      {storyData.step.script || storyData.script}
                    </p>

                    {/* Audio player if available */}
                    {storyData.step.audio && (
                      <div className="bg-accent/50 rounded-lg p-4">
                        <audio ref={audioRef} src={storyData.step.audio} preload="metadata" />
                        
                        <div className="flex items-center gap-4 mb-3">
                          <button
                            onClick={handlePlayPause}
                            className="p-2 bg-primary text-primary-foreground rounded-full hover:bg-primary/90 transition-colors"
                          >
                            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                          </button>
                          
                          <button
                            onClick={handleRestart}
                            className="p-2 bg-secondary text-secondary-foreground rounded-full hover:bg-secondary/90 transition-colors"
                          >
                            <RotateCcw className="h-4 w-4" />
                          </button>
                          
                          <button
                            onClick={handleMuteToggle}
                            className="p-2 bg-secondary text-secondary-foreground rounded-full hover:bg-secondary/90 transition-colors"
                          >
                            {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                          </button>
                        </div>

                        <div className="flex items-center gap-3">
                          <span className="text-xs text-muted-foreground min-w-[40px]">
                            {formatTime(currentTime)}
                          </span>
                          <input
                            type="range"
                            min="0"
                            max={duration || 0}
                            value={currentTime}
                            onChange={handleSeek}
                            className="flex-1 h-2 bg-secondary rounded-lg appearance-none cursor-pointer"
                          />
                          <span className="text-xs text-muted-foreground min-w-[40px]">
                            {formatTime(duration)}
                          </span>
                        </div>
                      </div>
                    )}
                  </motion.div>
                </div>
              ) : (
                // Text-only layout
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                  className="p-8 text-center min-h-[400px] flex flex-col justify-center"
                >
                  <h2 className="text-3xl font-bold text-card-foreground mb-6">
                    Chapter {currentStage}
                  </h2>
                  <p className="text-card-foreground leading-relaxed text-lg max-w-3xl mx-auto">
                    {storyData.step?.script || storyData.script}
                  </p>
                </motion.div>
              )}
            </motion.div>
          </AnimatePresence>
        )}
      </div>
    </MainLayout>
  )
}

export default StoryItemComponent

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  BookOpen,
  Loader2
} from 'lucide-react'
import MainLayout from '../../../../../components/layout/MainLayout'
import { cn } from '../../../../../utils/cn'

import { SingleStoryResponse } from '../../../../../services/story/storyService'

interface StoryItemComponentProps {
  taskSetId: string
  storyId: string
  storyData: SingleStoryResponse | null
  currentStoryIndex: number
  totalStories: number
  isLoading: boolean
  error: string | null
  onPreviousStory: () => void
  onNextStory: () => void
  onGoBack: () => void
  canGoPrevious: boolean
  canGoNext: boolean
}

/**
 * StoryItem Component - Pure UI component for story item page within tasks structure
 */
const StoryItemComponent: React.FC<StoryItemComponentProps> = ({
  taskSetId,
  storyId,
  storyData,
  currentStoryIndex,
  totalStories,
  isLoading,
  error,
  onPreviousStory,
  onNextStory,
  onGoBack,
  canGoPrevious,
  canGoNext
}) => {


  const getStoryTitle = () => {
    return `Story ${currentStoryIndex + 1} of ${totalStories}`
  }

  return (
    <MainLayout
      title={getStoryTitle()}
      description="Interactive story experience"
    >
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header with navigation */}
        <div className="flex items-center justify-between">
          <button
            onClick={onGoBack}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm">Back to Task Set</span>
          </button>

          {/* Story navigation */}
          {totalStories > 1 && (
            <div className="flex items-center gap-4">
              <button
                onClick={onPreviousStory}
                disabled={!canGoPrevious}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  !canGoPrevious
                    ? "text-muted-foreground cursor-not-allowed"
                    : "text-foreground hover:bg-accent"
                )}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>

              <div className="flex items-center gap-2">
                <BookOpen className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium">
                  Story {currentStoryIndex + 1} of {totalStories}
                </span>
              </div>

              <button
                onClick={onNextStory}
                disabled={!canGoNext}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  !canGoNext
                    ? "text-muted-foreground cursor-not-allowed"
                    : "text-foreground hover:bg-accent"
                )}
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>

        {/* Loading state */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center justify-center py-12"
          >
            <div className="flex items-center gap-3">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
              <span className="text-lg text-muted-foreground">Loading story...</span>
            </div>
          </motion.div>
        )}

        {/* Error state */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-destructive/10 border border-destructive/20 rounded-xl p-6"
          >
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <div>
                <h3 className="font-medium text-destructive">Error Loading Story</h3>
                <p className="text-sm text-destructive/80">{error}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Story content */}
        {!isLoading && !error && storyData && (
          <AnimatePresence mode="wait">
            <motion.div
              key={storyId}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              className="bg-card border border-border rounded-xl overflow-hidden shadow-lg"
            >
              {storyData.metadata?.url ? (
                // Image + text layout
                <div className="grid grid-cols-1 lg:grid-cols-2 min-h-[600px]">
                  <div className="relative bg-slate-100 flex items-center justify-center p-6">
                    <motion.img
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.6, ease: "easeOut" }}
                      src={storyData.metadata.url}
                      alt={`Story ${currentStoryIndex + 1}`}
                      className="w-full h-full object-contain rounded-xl max-h-[500px]"
                    />
                  </div>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.5, ease: "easeOut" }}
                    className="shrink-0 bg-card p-8 flex flex-col justify-center"
                  >
                    <h2 className="text-3xl font-bold text-card-foreground mb-6">
                      Story {currentStoryIndex + 1}
                    </h2>
                    <p className="text-card-foreground leading-relaxed text-lg mb-6">
                      {storyData.script}
                    </p>
                  </motion.div>
                </div>
              ) : (
                // Text-only layout
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                  className="p-8 text-center min-h-[400px] flex flex-col justify-center"
                >
                  <h2 className="text-3xl font-bold text-card-foreground mb-6">
                    Story {currentStoryIndex + 1}
                  </h2>
                  <p className="text-card-foreground leading-relaxed text-lg max-w-3xl mx-auto">
                    {storyData.script}
                  </p>
                </motion.div>
              )}
            </motion.div>
          </AnimatePresence>
        )}
      </div>
    </MainLayout>
  )
}

export default StoryItemComponent

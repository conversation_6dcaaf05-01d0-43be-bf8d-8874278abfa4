import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import TaskTypeBadge from '../../../../components/task/TaskTypeBadge'
import {
  Loader2,
  ArrowLeft,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Languages,
  Expand,
  X,
  HelpCircle,
  Play,
  ArrowRight,
  Mic, Square, StopCircle, Volume2, VolumeX
} from 'lucide-react'
import { Link } from 'react-router-dom'
import MainLayout from '../../../../components/layout/MainLayout'
import { TaskItemSkeleton } from '../../../../components/ui/Skeleton'
import TaskSetScoreDisplay from '../../../../components/ui/TaskSetScoreDisplay'
import { cn } from '../../../../utils/cn'
import AudioRecorder from '../../../../components/audio/AudioRecorder';

// Types for question object structure (matching old TaskItem)
interface QuestionMetadata {
  object_name: string
  bucket_name: string
  object_path: string
  file_name: string
  content_type: string
  size_bytes: number
  folder: string
  session_id: string
  created_at: string
  file_extension: string
  url: string
}

interface QuestionObject {
  text: string
  translated_text?: string
  options: {
    [key: string]: string // e.g., { a: "Option 1", b: "Option 2", c: "Option 3" }
  }
  media_url?: string | null
  metadata?: QuestionMetadata
  answer_hint?: string
}

interface TaskItemComponentProps {
  taskSetId: string
  taskItemId: string
  taskSet: any | null
  taskItem: any | null
  taskSetScore: any | null
  loading: boolean
  loadingScore?: boolean
  error: string | null
  submitting: boolean
  currentTaskIndex: number
  totalTasks: number
  onSubmitAnswer: (answer: { selected_option?: string; selected_options?: string[]; text_answer?: string }) => void
  onNavigateTask: (direction: 'next' | 'prev') => void
  onRefresh: () => void
}

/**
 * TaskItem Component - Pure UI component for individual task item page
 */
const TaskItemComponent: React.FC<TaskItemComponentProps> = ({
  taskSetId,
  taskItem,
  taskSetScore,
  loading,
  loadingScore = false,
  error,
  submitting,
  currentTaskIndex,
  totalTasks,
  onSubmitAnswer,
  onNavigateTask
}) => {
  const [selectedAnswer, setSelectedAnswer] = useState<string>('')
  const [textAnswer, setTextAnswer] = useState<string>('')
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)
  const [showTranslation, setShowTranslation] = useState<boolean>(false)
  const [showImagePreview, setShowImagePreview] = useState<boolean>(false)
  const [showStory, setShowStory] = useState<boolean>(false) // Will be set based on story availability

  // Check if task has story and ALWAYS show story first when navigating to a new task
  useEffect(() => {
    if (taskItem?.story && taskItem.story.script) {
      setShowStory(true) // Always show story first if available
    } else {
      setShowStory(false)
    }

    // Reset form states when navigating to a new task
    setSelectedAnswer('')
    setTextAnswer('')
    setShowTranslation(false)
    setShowImagePreview(false)
  }, [taskItem?.id, taskItem?._id]) // Reset when task ID changes (navigation)

  // Handle story completion - move to question with smooth transition
  const handleStoryComplete = () => {
    // Add a small delay for smooth transition
    setTimeout(() => {
      setShowStory(false)
    }, 300)
  }

  // Check if task has story
  const hasStory = () => {
    return taskItem?.story && taskItem.story.script
  }

  // Set initial values when task item loads or changes
  useEffect(() => {
    if (taskItem) {
      // Set selected answer if task is already answered
      if (taskItem.user_answer?.selected_option) {
        setSelectedAnswer(taskItem.user_answer.selected_option)
      } else if (taskItem.user_answer && typeof taskItem.user_answer === 'string') {
        setSelectedAnswer(taskItem.user_answer)
      }

      // Set text answer if available
      if (taskItem.user_answer?.text_answer) {
        setTextAnswer(taskItem.user_answer.text_answer)
      }

      // Set audio URL if available (for speak_word tasks)
      if (taskItem.type === 'speak_word' && taskItem.user_answer?.audio_url) {
        // We'll handle the audio URL in the AudioRecorder component directly
      }
    }
  }, [taskItem])

  // Helper functions for question object handling (from old TaskItem)
  const isQuestionObject = (question: string | QuestionObject | undefined): question is QuestionObject => {
    return typeof question === 'object' && question !== null && 'text' in question
  }

  const getQuestionText = (): string => {
    if (!taskItem?.question) return ''

    if (isQuestionObject(taskItem.question)) {
      return taskItem.question.text
    }

    return taskItem.question
  }

  const getQuestionOptionsWithKeys = (): Array<{key: string, value: string}> => {
    // Check if question is an object with options
    if (isQuestionObject(taskItem?.question) && taskItem.question.options) {
      // Return array of {key, value} pairs for new question format
      const options = taskItem.question.options
      return Object.keys(options).sort().map(key => ({
        key,
        value: options[key]
      }))
    }

    // For legacy format, create keys as indices
    const legacyOptions = taskItem?.options || []
    return legacyOptions.map((value: string, index: number) => ({
      key: String.fromCharCode(97 + index), // 'a', 'b', 'c', etc.
      value
    }))
  }

  const getTranslatedText = (): string => {
    if (isQuestionObject(taskItem?.question)) {
      return taskItem.question.translated_text || ''
    }
    return ''
  }

  const hasTranslation = (): boolean => {
    return getTranslatedText().length > 0
  }

  const getMediaUrl = (): string => {
    // Check question.metadata.url first (new format with metadata object)
    if (isQuestionObject(taskItem?.question) && taskItem.question.metadata?.url) {
      return taskItem.question.metadata.url
    }

    // Check question.media_url (for image identification tasks)
    if (isQuestionObject(taskItem?.question) && taskItem.question.media_url) {
      return taskItem.question.media_url
    }

    // Fallback to task.media_url
    return taskItem?.media_url || ''
  }

  const getQuestionTypeHint = (type: string): string => {
    const hints: Record<string, string> = {
      'single_choice': 'Select one correct answer from the options',
      'multiple_choice': 'Select all correct answers from the options',
      'image_identification': 'Look at the image and select the correct answer',
      'text_input': 'Type your answer in the text field',
      'speak_word': 'Record yourself speaking the word',
      'answer_in_word': 'Provide a single word answer'
    }
    return hints[type.toLowerCase()] || 'Complete this task'
  }

  const handleSubmit = async () => {
    if (!taskItem) return

    const answer: {
      selected_option?: string
      selected_options?: string[]
      text_answer?: string
      audio_blob?: Blob
    } = {}

    try {
      if (taskItem.type === 'single_choice' || taskItem.type === 'multiple_choice' ||
          taskItem.type === 'image_identification' ||
          taskItem.type === 'SINGLE_CHOICE' || taskItem.type === 'MULTIPLE_CHOICE' ||
          taskItem.type === 'IMAGE_IDENTIFICATION') {
        // Use the selected answer key directly (already in key format)
        answer.selected_option = selectedAnswer
      } else if (taskItem.type === 'text_input' || taskItem.type === 'TEXT_INPUT') {
        answer.text_answer = textAnswer
      } else if (taskItem.type === 'speak_word' || taskItem.type === 'SPEAK_WORD') {
        if (!audioBlob && !taskItem.user_answer?.audio_url) {
          // Show error if no audio is recorded
          return
        }
        // Pass the audio blob to be handled by the container
        answer.audio_blob = audioBlob || undefined
      }

      onSubmitAnswer(answer)
    } catch (error) {
      console.error('Error submitting answer:', error)
    }
  }

  // Handle single choice selection using option keys
  const handleSingleChoiceSelect = (optionKey: string) => {
    if (submitting || isAnswered) return
    setSelectedAnswer(optionKey)
  }

  // Check multiple ways a task can be considered answered (for compatibility)
  const isAnswered = taskItem?.status === 'COMPLETED' || taskItem?.submitted === true
  const isCorrect = taskItem?.result === 'CORRECT' || taskItem?.result === 'correct'

  return (
    <MainLayout
      title={taskItem ? getQuestionText() || 'Task Item' : 'Task Item'}
      description="Complete this task item"
    >
      <div className="h-[calc(100vh-4rem)] flex flex-col max-w-4xl mx-auto">
        {/* Compact Header */}
        <div className="flex items-center justify-between px-6 py-3 border-b border-border">
          <Link
            to={`/tasks/${taskSetId}`}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm">Previous</span>
          </Link>

          {/* Task Progress & Navigation */}
          <div className="flex items-center gap-4">
            {taskItem && (
              <div className="flex items-center gap-3 text-sm text-muted-foreground">
                <div className="flex items-center gap-1" title={getQuestionTypeHint(taskItem.type || '')}>
                  <span className="capitalize">{taskItem.type?.replace('_', ' ').toLowerCase()}</span>
                  <HelpCircle className="h-3 w-3" />
                </div>
                <span>•</span>
                <span>Score: {taskItem.scored || 0}/{taskItem.total_score || 0}</span>
                <span>•</span>
                <span>Task {currentTaskIndex + 1} of {totalTasks}</span>
                {hasStory() && (
                  <>
                    <span>•</span>
                    <div className="flex items-center gap-1">
                      <Play className="h-3 w-3 text-blue-600" />
                      <span className="text-blue-600 font-medium">Story</span>
                      {!showStory && (
                        <button
                          onClick={() => setShowStory(true)}
                          className="ml-1 text-xs text-blue-600 hover:text-blue-800 underline"
                          title="View story again"
                        >
                          (view again)
                        </button>
                      )}
                    </div>
                  </>
                )}
                {isAnswered && (
                  <>
                    <span>•</span>
                    <span className={cn(
                      "flex items-center gap-1 px-2 py-1 rounded-full text-xs",
                      isCorrect ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"
                    )}>
                      {isCorrect ? <CheckCircle className="h-3 w-3" /> : <XCircle className="h-3 w-3" />}
                      {isCorrect ? 'Correct' : 'Incorrect'}
                    </span>
                  </>
                )}
              </div>
            )}

            <div className="flex items-center gap-1">
              <button
                onClick={() => onNavigateTask('prev')}
                disabled={currentTaskIndex <= 0}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  currentTaskIndex <= 0
                    ? "cursor-not-allowed opacity-50"
                    : "hover:bg-accent"
                )}
                title={currentTaskIndex <= 0 ? "No previous task" : "Previous Task"}
              >
                <ChevronLeft className="h-4 w-4 text-muted-foreground" />
              </button>
              <button
                onClick={() => onNavigateTask('next')}
                disabled={currentTaskIndex >= totalTasks - 1}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  currentTaskIndex >= totalTasks - 1
                    ? "cursor-not-allowed opacity-50"
                    : "hover:bg-accent"
                )}
                title={currentTaskIndex >= totalTasks - 1 ? "No next task" : "Next Task"}
              >
                <ChevronRight className="h-4 w-4 text-muted-foreground" />
              </button>
            </div>
          </div>
        </div>

        {/* Error state */}
        {error && (
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4 max-w-md">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-destructive" />
                <div>
                  <h3 className="font-medium text-destructive">Error</h3>
                  <p className="text-sm text-destructive/80">{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Loading state */}
        {loading ? (
          <TaskItemSkeleton />
        ) : taskItem ? (
          <div className="flex-1 p-4 space-y-4 overflow-y-auto">
            {/* Task Set Score Display */}
            <TaskSetScoreDisplay
              score={taskSetScore}
              loading={loadingScore}
              className="mb-4"
            />

            {/* Story Display - Show first if task has story and not yet viewed */}
            <AnimatePresence mode="wait">
              {showStory && hasStory() && (
                <motion.div
                  key="story-display"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95, transition: { duration: 0.3 } }}
                  className="w-full h-[500px] relative overflow-hidden rounded-2xl mb-6"
                >
                {taskItem.story.media_url || taskItem.story.metadata?.url ? (
                  // Comic book style with background image
                  <div
                    className="w-full h-full relative overflow-hidden rounded-2xl"
                    style={{
                      backgroundImage: `url(${taskItem.story.metadata?.url || taskItem.story.media_url})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      backgroundRepeat: 'no-repeat',
                      backgroundColor: '#f8fafc' // Light gray fallback
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent flex items-end">
                      <div className="p-8 w-full">
                        <motion.div
                          initial={{ opacity: 0, y: 30 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.3, duration: 0.5, ease: "easeOut" }}
                          className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl border border-white/50"
                        >
                          <div className="flex items-center gap-2 mb-4">
                            <Play className="h-6 w-6 text-blue-600" />
                            <h2 className="text-2xl font-bold text-slate-900">
                              Story - Stage {taskItem.story.stage || 1}
                            </h2>
                          </div>
                          <p className="text-slate-800 leading-relaxed text-lg font-medium mb-6">
                            {taskItem.story.script}
                          </p>
                          <button
                            onClick={handleStoryComplete}
                            className="flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-lg"
                          >
                            Continue to Question
                            <ArrowRight className="h-4 w-4" />
                          </button>
                        </motion.div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Text-only story layout
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="w-full h-full bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 rounded-2xl p-8 flex flex-col justify-center items-center text-center"
                  >
                    <div className="max-w-2xl">
                      <div className="flex items-center justify-center gap-3 mb-6">
                        <Play className="h-8 w-8 text-blue-600" />
                        <h2 className="text-3xl font-bold text-slate-900 dark:text-slate-100">
                          Story - Stage {taskItem.story.stage || 1}
                        </h2>
                      </div>
                      <p className="text-slate-700 dark:text-slate-300 leading-relaxed text-xl mb-8">
                        {taskItem.story.script}
                      </p>
                      <button
                        onClick={handleStoryComplete}
                        className="flex items-center gap-2 bg-primary text-primary-foreground px-8 py-4 rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium text-lg mx-auto shadow-lg"
                      >
                        Continue to Question
                        <ArrowRight className="h-5 w-5" />
                      </button>
                    </div>
                  </motion.div>
                )}
              </motion.div>
              )}
            </AnimatePresence>

            {/* Question Section - Only show when story is not being displayed */}
            {!showStory && (
              <>
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={cn(
                    "text-center p-6 rounded-xl border-2 transition-all duration-300",
                    isAnswered
                      ? isCorrect
                        ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 shadow-green-100 dark:shadow-green-900/20"
                        : "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 shadow-red-100 dark:shadow-red-900/20"
                      : "bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700 shadow-lg hover:shadow-xl"
                  )}
                >
              {/* Status indicator */}
              {isAnswered && (
                <motion.div
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className={cn(
                    "inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium mb-4",
                    isCorrect
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                  )}
                >
                  {isCorrect ? (
                    <>
                      <CheckCircle className="h-4 w-4" />
                      Correct Answer!
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4" />
                      Incorrect Answer
                    </>
                  )}
                </motion.div>
              )}

              <div className="flex flex-col items-center gap-3 mb-4">
                <div className="flex items-center gap-3">
                  <h1 className={cn(
                    "text-2xl font-bold transition-colors",
                    isAnswered
                      ? isCorrect
                        ? "text-green-800 dark:text-green-200"
                        : "text-red-800 dark:text-red-200"
                      : "text-foreground"
                  )}>
                    {getQuestionText()}
                  </h1>
                </div>
                {taskItem?.type && (
                  <div className="flex items-center gap-2">
                    <TaskTypeBadge type={taskItem.type} />
                    <span className="text-xs text-muted-foreground">
                      Question {currentTaskIndex + 1} of {totalTasks}
                    </span>
                  </div>
                )}
                {hasTranslation() && (
                  <button
                    onClick={() => setShowTranslation(!showTranslation)}
                    className="flex items-center gap-1 px-3 py-1 text-sm border border-border rounded-lg hover:bg-accent transition-colors"
                    title="Toggle translation"
                  >
                    <Languages className="h-4 w-4" />
                    {showTranslation ? 'Original' : 'Translate'}
                  </button>
                )}
              </div>

              {showTranslation && hasTranslation() && (
                <p className="text-muted-foreground mb-3 p-3 bg-muted rounded-lg max-w-2xl mx-auto">
                  <span className="text-xs font-medium text-muted-foreground/70 uppercase tracking-wide">Translation:</span><br />
                  {getTranslatedText()}
                </p>
              )}
            </motion.div>

            {/* Media Content */}
            {(() => {
              const mediaUrl = getMediaUrl()
              const metadata = isQuestionObject(taskItem?.question) ? taskItem.question.metadata : null
              const isAudio = metadata?.content_type?.startsWith('audio/') || mediaUrl?.toLowerCase().endsWith('.wav') || mediaUrl?.toLowerCase().endsWith('.mp3')
              const isImage = !isAudio && (metadata?.content_type?.startsWith('image/') || 
                mediaUrl?.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp)$/))

              if (mediaUrl) {
                return (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex justify-center"
                  >
                    {isAudio ? (
                      <div className="w-full max-w-md bg-white dark:bg-slate-800 p-4 rounded-xl shadow-lg">
                        <div className="flex items-center justify-center gap-3 mb-2">
                          <Volume2 className="h-6 w-6 text-blue-600" />
                          <h3 className="text-lg font-medium">Listen to the audio</h3>
                        </div>
                        <audio 
                          src={mediaUrl} 
                          controls 
                          className="w-full mt-2"
                        >
                          Your browser does not support the audio element.
                        </audio>
                      </div>
                    ) : isImage ? (
                      <div className="relative group">
                        <img
                          src={mediaUrl}
                          alt="Task media"
                          className="max-w-sm max-h-80 object-contain rounded-lg shadow-lg cursor-pointer transition-transform hover:scale-105"
                          onClick={() => setShowImagePreview(true)}
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors flex items-center justify-center">
                          <Expand className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                        </div>
                        <div className="absolute top-2 right-2 bg-black/60 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                          Click to expand
                        </div>
                      </div>
                    ) : (
                      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                          Unsupported media type: {metadata?.content_type || 'Unknown'}
                        </p>
                      </div>
                    )}
                  </motion.div>
                )
              }
              return null
            })()}

            {/* Answer Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="max-w-2xl mx-auto"
            >

              {/* Single/Multiple Choice and Image Identification Options */}
              {(taskItem.type === 'single_choice' || taskItem.type === 'multiple_choice' ||
                taskItem.type === 'image_identification' ||
                taskItem.type === 'SINGLE_CHOICE' || taskItem.type === 'MULTIPLE_CHOICE' ||
                taskItem.type === 'IMAGE_IDENTIFICATION') && (
                (() => {
                  const optionsWithKeys = getQuestionOptionsWithKeys()

                  if (optionsWithKeys.length === 0) {
                    return (
                      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg mb-6">
                        <p className="text-yellow-800 dark:text-yellow-200 text-sm text-center">
                          No options available for this task.
                        </p>
                      </div>
                    )
                  }

                  return (
                    <div className="mb-4">
                      <div className="grid grid-cols-2 gap-2">
                        {optionsWithKeys.map((option, index) => {
                          const isSelected = selectedAnswer === option.key
                          const isDisabled = submitting || isAnswered

                          // Check if this option was the user's answer
                          const isUserAnswer = isAnswered && (
                            taskItem.user_answer?.selected_option === option.key ||
                            taskItem.user_answer === option.key
                          )

                          // Check if this is the correct answer
                          const isCorrectAnswer = isAnswered && (
                            taskItem.correct_answer?.selected_option === option.key ||
                            taskItem.correct_answer === option.key ||
                            taskItem.answer === option.key
                          )

                          return (
                            <motion.button
                              key={option.key}
                              className={cn(
                                "w-full text-left p-3 border rounded-lg transition-colors text-sm font-medium min-h-[50px] flex items-center",
                                // Not answered yet - show selection state
                                !isAnswered && isSelected && "bg-purple-100 dark:bg-purple-900/70 border-purple-500 dark:border-purple-400 text-purple-800 dark:text-purple-200",
                                !isAnswered && !isSelected && "hover:bg-purple-50 dark:hover:bg-purple-900/40 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:border-purple-300 dark:hover:border-purple-500",
                                // Already answered - show result-based styling
                                isAnswered && isUserAnswer && isCorrect && "bg-green-100 dark:bg-green-900/70 border-green-500 dark:border-green-400 text-green-800 dark:text-green-200",
                                isAnswered && isUserAnswer && !isCorrect && "bg-red-100 dark:bg-red-900/70 border-red-500 dark:border-red-400 text-red-800 dark:text-red-200",
                                isAnswered && !isUserAnswer && isCorrectAnswer && "bg-green-100 dark:bg-green-900/70 border-green-500 dark:border-green-400 text-green-800 dark:text-green-200",
                                isAnswered && !isUserAnswer && !isCorrectAnswer && "bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 border-gray-300 dark:border-gray-600",
                                // Disabled state
                                isDisabled && "cursor-not-allowed"
                              )}
                              onClick={() => handleSingleChoiceSelect(option.key)}
                              disabled={isDisabled}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                              whileHover={!isDisabled ? { scale: 1.02 } : {}}
                              whileTap={!isDisabled ? { scale: 0.98 } : {}}
                            >
                              <div className="flex items-center justify-between w-full">
                                <span className="flex-1">{option.value}</span>
                                {isAnswered && isCorrectAnswer && (
                                  <CheckCircle className="h-5 w-5 text-green-600 ml-2" />
                                )}
                              </div>
                            </motion.button>
                          )
                        })}
                      </div>
                    </div>
                  )
                })()
              )}

              {/* Text Input */}
              {(taskItem.type === 'text_input' || taskItem.type === 'TEXT_INPUT') && (
                <div className="mb-4">
                  <textarea
                    value={textAnswer}
                    onChange={(e) => setTextAnswer(e.target.value)}
                    disabled={isAnswered}
                    placeholder="Enter your answer..."
                    className="w-full p-3 border border-border rounded-lg resize-none h-24 bg-background text-foreground"
                  />
                  {isAnswered && taskItem.user_answer?.text_answer && (
                    <div className="mt-2 p-2 bg-muted rounded-lg">
                      <p className="text-xs text-muted-foreground mb-1">Your answer:</p>
                      <p className="text-sm text-foreground">{taskItem.user_answer.text_answer}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Audio Recording for Speak Word */}
              {(taskItem.type === 'speak_word' || taskItem.type === 'SPEAK_WORD') && (
                <div className="mb-4">
                  <div className="p-4 border border-border rounded-lg bg-muted/20">
                    <div className="flex items-center gap-2 mb-3 text-amber-600 dark:text-amber-400">
                      <Mic className="h-5 w-5" />
                      <h3 className="font-medium">Speak the Word</h3>
                    </div>
                    <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-100 dark:border-amber-800 rounded-lg p-3 mb-4">
                      <p className="text-sm font-medium text-amber-800 dark:text-amber-200 mb-1">
                        {taskItem.question.answer_hint ? 'Speak this word:' : 'Speak clearly into the microphone'}
                      </p>
                      {taskItem.question.answer_hint && (
                        <p className="text-2xl font-bold text-amber-900 dark:text-amber-100">
                          {taskItem.question.answer_hint}
                        </p>
                      )}
                      {!taskItem.question.answer_hint && (
                        <p className="text-sm text-amber-700/80 dark:text-amber-300/80">
                          Click the microphone to start recording.
                        </p>
                      )}
                    </div>
                    
                    <div className="w-full">
                      <AudioRecorder
                        onRecordingComplete={(blob) => setAudioBlob(blob)}
                        disabled={isAnswered}
                        initialAudioUrl={taskItem.user_answer?.audio_url || null}
                        maxDuration={15}
                      />
                      <p className="text-xs text-muted-foreground mt-2 text-center">
                        {!audioBlob && !taskItem.user_answer?.audio_url 
                          ? 'Click the microphone to start recording' 
                          : 'Click the play button to listen to your recording'}
                      </p>
                    </div>
                    
                    {isAnswered && taskItem.user_answer?.audio_url && (
                      <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                        <p className="text-xs text-muted-foreground mb-2">Your recording:</p>
                        <audio 
                          src={taskItem.user_answer.audio_url} 
                          controls 
                          className="w-full"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Submit Button & Navigation */}
              <div className="flex items-center justify-between gap-4">
                {!isAnswered ? (
                  <>
                    <div className="text-sm text-muted-foreground">
                      {taskItem.type === 'speak_word' || taskItem.type === 'SPEAK_WORD' 
                        ? 'Record your voice before submitting' 
                        : 'Review your answer before submitting'}
                    </div>
                    <button
                      onClick={handleSubmit}
                      disabled={submitting || 
                        (taskItem.type === 'speak_word' || taskItem.type === 'SPEAK_WORD' 
                          ? !audioBlob && !taskItem.user_answer?.audio_url
                          : !selectedAnswer && !textAnswer)
                      }
                      className={cn(
                        "px-6 py-3 rounded-lg font-medium transition-all",
                        "bg-primary text-primary-foreground hover:bg-primary/90",
                        "disabled:opacity-50 disabled:cursor-not-allowed"
                      )}
                    >
                      {submitting ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Submitting...
                        </div>
                      ) : (
                        'Submit Answer'
                      )}
                    </button>
                  </>
                ) : (
                  <div className={cn(
                    "w-full p-4 rounded-lg border text-center",
                    isCorrect ? "border-green-500 bg-green-50 dark:bg-green-900/20" : "border-red-500 bg-red-50 dark:bg-red-900/20"
                  )}>
                    <div className="flex items-center justify-center gap-2 mb-2">
                      {isCorrect ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                      <span className={cn(
                        "font-medium",
                        isCorrect ? "text-green-600" : "text-red-600"
                      )}>
                        {isCorrect ? 'Correct!' : 'Incorrect'}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      You scored {taskItem.scored || 0} out of {taskItem.total_score || 0} points
                    </p>
                  </div>
                )}
              </div>
            </motion.div>
              </>
            )}
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center bg-card border border-border rounded-lg p-6 max-w-md"
            >
              <div className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-muted mb-3">
                <AlertCircle className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2 text-card-foreground">Task not found</h3>
              <p className="text-muted-foreground text-sm">
                The requested task could not be found.
              </p>
            </motion.div>
          </div>
        )}
      </div>

      {/* Full-screen image preview modal */}
      <AnimatePresence>
        {showImagePreview && (
          <motion.div
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowImagePreview(false)}
          >
            <motion.div
              className="relative max-w-full max-h-full"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={getMediaUrl()}
                alt="Task media - Full view"
                className="max-w-full max-h-full object-contain rounded-lg"
              />
              <button
                className="absolute top-4 right-4 bg-black/60 text-white p-2 rounded-full hover:bg-black/80 transition-all"
                onClick={() => setShowImagePreview(false)}
              >
                <X className="h-6 w-6" />
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </MainLayout>
  )
}

export default TaskItemComponent

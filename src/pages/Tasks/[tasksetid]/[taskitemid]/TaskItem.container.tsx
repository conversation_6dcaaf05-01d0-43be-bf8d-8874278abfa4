import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '../../../../store/hooks'
import { taskService } from '../../../../services/task/taskService'
import { httpBase } from '../../../../services/http/httpBase'

import { useNavigation } from '../../../../contexts/NavigationContext'
import TaskItemComponent from './TaskItem.component'
import Alert from '../../../../components/ui/Alert'
import CompletionModal from '../../../../components/ui/CompletionModal'
import {
  selectTaskSet,
  selectTaskItem,
  selectTaskSetScore,
  selectIsTaskSetCached,
  selectIsTaskItemCached,
  selectIsTaskSetScoreCached,
  selectIsCacheValid,
  setCurrentTaskSetId,
  setTaskSet,
  setTaskItem,
  setTaskSetScore,
  setLoadingTaskSet,
  setLoadingTaskItem,
  setLoadingTaskSetScore,
  setError,
  clearError,
  updateTaskItem,
  clearTaskSetCache
} from '../../../../store/slices/taskSlice'

interface TaskItemState {
  taskList: string[] // List of task IDs in the task set for navigation
  currentTaskIndex: number
  submitting: boolean
  alert: {
    open: boolean
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    description?: string
    correctAnswers?: string | string[]
  }
  showCompletionModal: boolean
  autoNavigateTimer: NodeJS.Timeout | null
}

/**
 * TaskItem Container - Handles logic and state for individual task item page
 */
const TaskItemContainer: React.FC = () => {
  const { tasksetid, taskitemid } = useParams<{ tasksetid: string; taskitemid: string }>()
  const { user, isAuthenticated } = useAppSelector((state) => state.auth)
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const { setBreadcrumbs, setCurrentTaskSet, setCurrentTaskItem } = useNavigation()

  // Get data from Redux store
  const taskSet = useAppSelector((state) => tasksetid ? selectTaskSet(state, tasksetid) : null)
  const taskItem = useAppSelector((state) => taskitemid ? selectTaskItem(state, taskitemid) : null)
  const taskSetScore = useAppSelector((state) => tasksetid ? selectTaskSetScore(state, tasksetid) : null)
  const isTaskSetCached = useAppSelector((state) => tasksetid ? selectIsTaskSetCached(state, tasksetid) : false)
  const isTaskItemCached = useAppSelector((state) => taskitemid ? selectIsTaskItemCached(state, taskitemid) : false)
  const isTaskSetScoreCached = useAppSelector((state) => tasksetid ? selectIsTaskSetScoreCached(state, tasksetid) : false)
  const isTaskSetCacheValid = useAppSelector((state) => tasksetid ? selectIsCacheValid(state, `taskset_${tasksetid}`) : false)
  const isTaskItemCacheValid = useAppSelector((state) => taskitemid ? selectIsCacheValid(state, `taskitem_${taskitemid}`) : false)
  const isTaskSetScoreCacheValid = useAppSelector((state) => tasksetid ? selectIsCacheValid(state, `score_${tasksetid}`) : false)
  const loading = useAppSelector((state) => state.task.loadingTaskSet || state.task.loadingTaskItem)
  const loadingScore = useAppSelector((state) => tasksetid ? state.task.loadingTaskSetScore[tasksetid] || false : false)
  const error = useAppSelector((state) => state.task.error)

  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [state, setState] = useState<TaskItemState>({
    taskList: [],
    currentTaskIndex: 0,
    submitting: false,
    alert: {
      open: false,
      type: 'info',
      title: '',
      description: ''
    },
    showCompletionModal: false,
    autoNavigateTimer: null
  })

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, navigate])

  // Fetch task set and task item based on URL with caching
  useEffect(() => {
    const fetchTaskData = async () => {
      if (!tasksetid || !taskitemid || !user) return

      dispatch(clearError())

      try {
        let fetchedTaskSet = taskSet
        let fetchedTaskItem = taskItem

        // Check if task set is cached and valid
        if (!isTaskSetCached || !isTaskSetCacheValid) {
          dispatch(setLoadingTaskSet(true))
          fetchedTaskSet = await taskService.getTaskSet(tasksetid, false, {
            onError: (error) => {
              dispatch(setError(error.message))
            }
          })
          // Cache the task set
          dispatch(setTaskSet({ id: tasksetid, taskSet: fetchedTaskSet }))
          dispatch(setCurrentTaskSetId(tasksetid))
        }

        // Check if task item is cached and valid
        if (!isTaskItemCached || !isTaskItemCacheValid) {
          dispatch(setLoadingTaskItem(true))
          fetchedTaskItem = await taskService.getTaskItem(
            taskitemid,
            {
              onError: (error) => {
                dispatch(setError(error.message))
              }
            },
            // true // Include type and title in the response
          )
          // Cache the task item
          dispatch(setTaskItem({ id: taskitemid, taskItem: fetchedTaskItem }))
        }

        // Fetch task set score if not cached or invalid
        if (!isTaskSetScoreCached || !isTaskSetScoreCacheValid) {
          dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: true }))
          try {
            const scoreData = await taskService.getTaskSetScore(tasksetid, {
              onError: (error) => {
                console.error('Error fetching task set score:', error)
              }
            })
            dispatch(setTaskSetScore({ id: tasksetid, score: scoreData }))
          } catch (scoreError) {
            console.error('Failed to fetch task set score:', scoreError)
          } finally {
            dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: false }))
          }
        }

        // Use task IDs from task set for navigation
        const taskList = fetchedTaskSet?.tasks || []
        const currentTaskIndex = taskList.findIndex(taskId => taskId === taskitemid)

        // Helper function to get task set name from input_content
        const getTaskSetName = (input_content: any): string => {
          if (!input_content) return 'Task Set'

          // If it's a string, return it
          if (typeof input_content === 'string') {
            return input_content
          }

          // If it's an object, extract meaningful name
          if (typeof input_content === 'object') {
            return input_content.file_name ||
                   input_content.object_name ||
                   'Audio Task Set'
          }

          return 'Task Set'
        }

        const taskSetName = getTaskSetName(fetchedTaskSet?.input_content)

        // Update navigation context
        setCurrentTaskSet({ id: tasksetid, name: taskSetName })

        // Handle question text based on type (string or QuestionObject)
        const questionText = typeof fetchedTaskItem?.question === 'string'
          ? fetchedTaskItem.question
          : fetchedTaskItem?.question?.text || 'Task Item'

        setCurrentTaskItem({ id: taskitemid, name: questionText })

        setBreadcrumbs([
          {
            id: tasksetid,
            name: taskSetName,
            path: `/tasks/${tasksetid}`,
            type: 'taskset',
            isActive: false
          },
          {
            id: taskitemid,
            name: questionText,
            path: `/tasks/${tasksetid}/taskitem/${taskitemid}`,
            type: 'taskitem',
            isActive: true
          }
        ])

        setState(prev => ({
          ...prev,
          taskList,
          currentTaskIndex
        }))

      } catch (err) {
        console.error('Error fetching task data:', err)
        dispatch(setError('Failed to load task. Please try again.'))
      } finally {
        dispatch(setLoadingTaskSet(false))
        dispatch(setLoadingTaskItem(false))
      }
    }

    if (isAuthenticated && user && tasksetid && taskitemid) {
      fetchTaskData()
    }
  }, [isAuthenticated, user, tasksetid, taskitemid, isTaskSetCached, isTaskSetCacheValid, isTaskItemCached, isTaskItemCacheValid])

  // Helper function to map task types to API format
  const mapTaskTypeToApiFormat = (taskType: string): string => {
    const typeMapping: Record<string, string> = {
      'single_choice': 'single_choice',
      'SINGLE_CHOICE': 'single_choice',
      'multiple_choice': 'multiple_choice',
      'MULTIPLE_CHOICE': 'multiple_choice',
      'image_identification': 'image_identification',
      'IMAGE_IDENTIFICATION': 'image_identification',
      'text_input': 'text_input',
      'TEXT_INPUT': 'text_input'
    }
    return typeMapping[taskType] || taskType.toLowerCase()
  }

  // Upload audio file to media endpoint
  const uploadAudioFile = async (audioBlob: Blob, folder: string): Promise<{url: string, object_name: string}> => {
    const formData = new FormData()
    formData.append('file', audioBlob, `audio_${Date.now()}.wav`)
    formData.append('folder', folder)

    const response = await httpBase.post('/management/media/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })

    return {
      url: response.data.url,
      object_name: response.data.object_name
    }
  }

  // Helper to get answer hint from task item
  const getAnswerHint = (taskItem: any): string => {
    if (!taskItem?.question) return 'audio_responses';
    
    if (typeof taskItem.question === 'string') {
      return 'audio_responses';
    }
    
    return taskItem.question.answer_hint || 'audio_responses';
  }

  // Submit task item answer
  const handleSubmitAnswer = async (answer: {
    selected_option?: string
    selected_options?: string[]
    text_answer?: string
    audio_blob?: Blob
  }) => {
    if (!taskitemid || !taskItem) return

    try {
      setState(prev => ({ ...prev, submitting: true }))
      setIsUploading(true)

      // Initialize API answer object
      const apiAnswer: {
        answer: string | string[]
        task_type: string
        folder?: string
      } = {
        answer: '',
        task_type: taskItem.type.toLowerCase(),
      }

      let answerValue: string | string[] = ''
      const folder = getAnswerHint(taskItem)

      // Handle audio upload if this is a speak_word task
      if ((taskItem.type === 'speak_word' || taskItem.type === 'SPEAK_WORD') && answer.audio_blob) {
        try {
          const { url, object_name } = await uploadAudioFile(answer.audio_blob, folder)
          answerValue = url
          // Use the object_name as the folder for the submission
          apiAnswer.folder = object_name
        } catch (error) {
          console.error('Error uploading audio:', error)
          setState(prev => ({
            ...prev,
            alert: {
              open: true,
              type: 'error',
              title: 'Upload Failed',
              description: 'Failed to upload audio. Please try again.'
            }
          }))
          return
        }
      } else {
        answerValue = answer.selected_option || answer.text_answer || answer.selected_options || ''
      }

      // Update the answer value in the API answer object
      apiAnswer.answer = answerValue

      // For multiple choice, ensure answer is an array
      if (taskItem.type === 'multiple_choice' || taskItem.type === 'MULTIPLE_CHOICE') {
        if (Array.isArray(answerValue)) {
          apiAnswer.answer = answerValue
        } else if (answerValue) {
          apiAnswer.answer = [answerValue]
        }
      }

      // Submit using the new API format
      await taskService.submitTaskItem(taskitemid, apiAnswer, {
        onSuccess: (responseData) => {
          console.log('✅ Task submission response:', responseData)

          try {
            // Update the cached task item with submission result from response
            const updates: any = {
              submitted: true,
              submitted_at: new Date().toISOString(),
              user_answer: answer.selected_option || answer.text_answer || answer.selected_options,
              status: 'COMPLETED'
            }

            // Extract result data from response if available
            let isCorrect = false
            let correctAnswers: string | string[] | undefined = undefined

            if (responseData) {
              // Handle nested data structure - API returns { success: true, data: {...}, error: null, meta: {...} }
              const data = responseData.data || responseData

              // Map API response fields to our task item structure based on actual backend response
              const apiData = data as any

              if (apiData.scored !== undefined) {
                updates.scored = apiData.scored
              }
              if (apiData.total_score !== undefined) {
                updates.total_score = apiData.total_score
              }
              if (apiData.correct_answer) {
                updates.correct_answer = apiData.correct_answer
              }
              if (apiData.is_correct !== undefined) {
                isCorrect = apiData.is_correct
                updates.result = isCorrect ? 'CORRECT' : 'INCORRECT'
              }

              // Extract correct answers for alert display
              if (apiData.correct_answer?.display_text) {
                correctAnswers = apiData.correct_answer.display_text
              }
            }

            // Update cache with response data - no need to refetch!
            dispatch(updateTaskItem({
              id: taskitemid,
              updates
            }))

            // Show alert with result
            setState(prev => ({
              ...prev,
              alert: {
                open: true,
                type: isCorrect ? 'success' : 'error',
                title: isCorrect ? 'Correct!' : 'Incorrect',
                description: isCorrect
                  ? `Great job! You scored ${updates.scored || 0} out of ${updates.total_score || 0} points.`
                  : `You scored ${updates.scored || 0} out of ${updates.total_score || 0} points.`,
                correctAnswers: !isCorrect ? correctAnswers : undefined
              }
            }))

            // Refetch task set score after submission to get updated scores
            if (tasksetid) {
              refetchTaskSetScore()
            }

            // Auto-navigate to next task after a delay (only for correct answers)
            if (isCorrect) {
              const timer = setTimeout(() => {
                handleAutoNavigateNext()
              }, 2000) // 2 second delay

              setState(prev => ({
                ...prev,
                autoNavigateTimer: timer
              }))
            }

            console.log('✅ Updated task item cache with:', updates)
          } catch (parseError) {
            console.error('Error parsing submission response:', parseError)
            // Still show a success message since the API call succeeded
            setState(prev => ({
              ...prev,
              alert: {
                open: true,
                type: 'success',
                title: 'Submitted!',
                description: 'Your answer has been submitted successfully.',
                correctAnswers: undefined
              }
            }))
          }
        },
        onError: (error) => {
          dispatch(setError(error.message))
        }
      })
    } catch (err) {
      console.error('Error submitting answer:', err)
      dispatch(setError('Failed to submit answer. Please try again.'))
    } finally {
      setState(prev => ({ ...prev, submitting: false }))
      setIsUploading(false)
    }
  }

  // Auto-navigate to next task or show completion
  const handleAutoNavigateNext = () => {
    if (!tasksetid || state.taskList.length === 0 || state.currentTaskIndex === -1) {
      return
    }

    const nextIndex = state.currentTaskIndex + 1

    // Check if this is the last task
    if (nextIndex >= state.taskList.length) {
      // All tasks completed - show completion modal
      setState(prev => ({
        ...prev,
        showCompletionModal: true,
        alert: { ...prev.alert, open: false }
      }))
      return
    }

    // Navigate to next task
    const nextTaskId = state.taskList[nextIndex]
    if (nextTaskId) {
      // Refetch score when navigating to ensure we have the latest data
      refetchTaskSetScore()
      navigate(`/tasks/${tasksetid}/taskitem/${nextTaskId}`)
    }
  }

  // Navigate to next/previous task
  const handleNavigateTask = (direction: 'next' | 'prev') => {
    if (!tasksetid || state.taskList.length === 0 || state.currentTaskIndex === -1) {
      return
    }

    let newIndex: number
    if (direction === 'next') {
      newIndex = state.currentTaskIndex + 1
      // If at the end, don't navigate (or optionally loop to beginning)
      if (newIndex >= state.taskList.length) {
        return // Don't navigate past the last task
      }
    } else {
      newIndex = state.currentTaskIndex - 1
      // If at the beginning, don't navigate (or optionally loop to end)
      if (newIndex < 0) {
        return // Don't navigate before the first task
      }
    }

    // Get the task ID for the new index (taskList is now an array of task IDs)
    const nextTaskId = state.taskList[newIndex]

    if (nextTaskId) {
      // Refetch score when navigating to ensure we have the latest data
      refetchTaskSetScore()
      // Navigate to the new task
      navigate(`/tasks/${tasksetid}/taskitem/${nextTaskId}`)
    }
  }

  // Handle alert close
  const handleAlertClose = () => {
    // Clear auto-navigate timer if it exists
    if (state.autoNavigateTimer) {
      clearTimeout(state.autoNavigateTimer)
    }

    setState(prev => ({
      ...prev,
      alert: {
        ...prev.alert,
        open: false
      },
      autoNavigateTimer: null
    }))
  }

  // Handle completion modal actions
  const handleCompletionModalClose = () => {
    setState(prev => ({
      ...prev,
      showCompletionModal: false
    }))
  }

  const handleRetryTasks = () => {
    // Clear cache for this task set to allow fresh start
    if (tasksetid) {
      dispatch(clearTaskSetCache(tasksetid))
      // Navigate back to the task set detail page to retry all tasks
      navigate(`/tasks/${tasksetid}`)
    }
  }

  const handleMoveToStories = async () => {
    // Fetch stories for this task set and navigate to the first one
    if (tasksetid) {
      try {
        // Get task set with story IDs
        const taskSetWithStories = await taskService.getTaskSetWithStoryIds(tasksetid)

        if (taskSetWithStories.stories && taskSetWithStories.stories.length > 0) {
          // Navigate to the first story
          const firstStoryId = taskSetWithStories.stories[0]
          navigate(`/story/${firstStoryId}?stage=1`)
        } else {
          // No stories available, show a message or navigate to stories page
          navigate('/stories')
        }
      } catch (error) {
        console.error('Failed to fetch stories:', error)
        // Fallback to general stories page
        navigate('/stories')
      }
    }
  }

  // Function to refetch task set score
  const refetchTaskSetScore = async () => {
    if (!tasksetid) return

    try {
      dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: true }))
      const scoreData = await taskService.getTaskSetScore(tasksetid)
      dispatch(setTaskSetScore({ id: tasksetid, score: scoreData }))
    } catch (error) {
      console.error('Failed to refetch task set score:', error)
    } finally {
      dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: false }))
    }
  }

  // Cleanup timer on unmount
  React.useEffect(() => {
    return () => {
      if (state.autoNavigateTimer) {
        clearTimeout(state.autoNavigateTimer)
      }
    }
  }, [state.autoNavigateTimer])

  // Refresh data
  const handleRefresh = async () => {
    if (!tasksetid || !taskitemid || !user) return

    try {
      dispatch(setLoadingTaskSet(true))
      dispatch(setLoadingTaskItem(true))
      dispatch(clearError())

      const fetchedTaskSet = await taskService.getTaskSet(tasksetid, false)
      const fetchedTaskItem = await taskService.getTaskItem(taskitemid)

      // Update cache
      dispatch(setTaskSet({ id: tasksetid, taskSet: fetchedTaskSet }))
      dispatch(setTaskItem({ id: taskitemid, taskItem: fetchedTaskItem }))

      // Also refresh the task set score
      await refetchTaskSetScore()

      // Use task IDs from task set for navigation
      const taskList = fetchedTaskSet.tasks || []
      const currentTaskIndex = taskList.findIndex(taskId => taskId === taskitemid)

      setState(prev => ({
        ...prev,
        taskList,
        currentTaskIndex
      }))
    } catch (err) {
      console.error('Error refreshing task data:', err)
      dispatch(setError('Failed to refresh data. Please try again.'))
    } finally {
      dispatch(setLoadingTaskSet(false))
      dispatch(setLoadingTaskItem(false))
    }
  }

  if (!isAuthenticated || !user) {
    return null
  }

  if (!tasksetid || !taskitemid) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-foreground mb-2">Invalid Task</h2>
          <p className="text-muted-foreground">Task set ID or task item ID not provided.</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <TaskItemComponent
        taskSetId={tasksetid}
        taskItemId={taskitemid}
        taskSet={taskSet}
        taskItem={taskItem}
        taskSetScore={taskSetScore}
        loading={loading}
        loadingScore={loadingScore}
        error={error}
        submitting={state.submitting}
        currentTaskIndex={state.currentTaskIndex}
        totalTasks={state.taskList.length}
        onSubmitAnswer={handleSubmitAnswer}
        onNavigateTask={handleNavigateTask}
        onRefresh={handleRefresh}
      />

      {/* Alert for submission results */}
      <Alert
        open={state.alert.open}
        onOpenChange={handleAlertClose}
        type={state.alert.type}
        title={state.alert.title}
        description={state.alert.description}
        correctAnswers={state.alert.correctAnswers}
        showAutoNavigate={state.alert.type === 'success' && state.currentTaskIndex < state.taskList.length - 1}
        autoNavigateSeconds={2}
      />

      {/* Completion Modal */}
      <CompletionModal
        open={state.showCompletionModal}
        onOpenChange={handleCompletionModalClose}
        onRetryTasks={handleRetryTasks}
        onMoveToStories={handleMoveToStories}
        taskSetId={tasksetid}
        taskSetTitle={
          typeof taskSet?.input_content === 'object' && taskSet?.input_content?.file_name
            ? taskSet.input_content.file_name
            : 'Task Set'
        }
        totalTasks={state.taskList.length}
        totalScore={taskSet?.total_score || 0}
        userScore={taskSet?.scored || 0}
      />
    </>
  )
}

export default TaskItemContainer

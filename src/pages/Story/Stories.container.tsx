import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { storyService, StoryListItem } from '../../services/story/storyService'
import StoriesComponent from './Stories.component'

interface StoriesContainerState {
  stories: StoryListItem[]
  isLoading: boolean
  error: string | null
  currentPage: number
  totalPages: number
  totalStories: number
}

const StoriesContainer: React.FC = () => {
  const navigate = useNavigate()
  
  const [state, setState] = useState<StoriesContainerState>({
    stories: [],
    isLoading: true,
    error: null,
    currentPage: 1,
    totalPages: 1,
    totalStories: 0
  })

  useEffect(() => {
    fetchStories(1)
  }, [])

  const fetchStories = async (page: number) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      const response = await storyService.getFilteredStories(page, 10)
      
      setState(prev => ({
        ...prev,
        stories: response.data,
        currentPage: response.meta.page,
        totalPages: response.meta.total_pages,
        totalStories: response.meta.total,
        isLoading: false
      }))
    } catch (error) {
      console.error('Error fetching stories:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load stories',
        isLoading: false
      }))
    }
  }

  const handleStorySelect = (storyId: string) => {
    navigate(`/story/${storyId}?stage=1`)
  }

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= state.totalPages) {
      fetchStories(page)
    }
  }

  const handleRetry = () => {
    fetchStories(state.currentPage)
  }

  const handleRefresh = () => {
    fetchStories(1)
  }

  return (
    <StoriesComponent
      stories={state.stories}
      isLoading={state.isLoading}
      error={state.error}
      currentPage={state.currentPage}
      totalPages={state.totalPages}
      totalStories={state.totalStories}
      onStorySelect={handleStorySelect}
      onPageChange={handlePageChange}
      onRetry={handleRetry}
      onRefresh={handleRefresh}
    />
  )
}

export default StoriesContainer

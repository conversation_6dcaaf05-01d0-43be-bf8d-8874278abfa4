import React, { useEffect } from 'react'
import { useParams, useNavigate, useSearchParams } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  BookOpen,
  Loader2
} from 'lucide-react'
import { useStoryCache } from '../../../hooks/useStoryCache'
import { useAppDispatch } from '../../../store/hooks'
import { markStoryAsVisited, undismissStoryNotification } from '../../../store/slices/storySlice'
import { cn } from '../../../utils/cn'
import StagePage from './[stage]/StagePage'

const StoryLayout: React.FC = () => {
  const { story_id } = useParams<{ story_id: string }>()
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const [searchParams, setSearchParams] = useSearchParams()

  // Extract current stage from query parameter
  const currentStage = parseInt(searchParams.get('stage') || '1', 10)

  const {
    storyData,
    isLoading,
    error,
    totalStages,
    navigateToStage
  } = useStoryCache(story_id || '', currentStage)

  // Mark story as visited when user navigates to it
  useEffect(() => {
    if (story_id) {
      dispatch(markStoryAsVisited(story_id))
      dispatch(undismissStoryNotification(story_id))
    }
  }, [story_id, dispatch])

  // Redirect to stage 1 if no stage specified
  useEffect(() => {
    if (story_id && !searchParams.get('stage')) {
      setSearchParams({ stage: '1' }, { replace: true })
    }
  }, [story_id, searchParams, setSearchParams])

  const handleStageChange = (newStage: number) => {
    if (!story_id || newStage < 1 || newStage > totalStages) return

    setSearchParams({ stage: newStage.toString() })
    navigateToStage(newStage)
  }

  const handlePreviousStage = () => {
    if (currentStage > 1) {
      handleStageChange(currentStage - 1)
    }
  }

  const handleNextStage = () => {
    if (currentStage < totalStages) {
      handleStageChange(currentStage + 1)
    }
  }

  const handleGoBack = () => {
    navigate('/stories')
  }

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft' && currentStage > 1) {
        handlePreviousStage()
      } else if (e.key === 'ArrowRight' && currentStage < totalStages) {
        handleNextStage()
      } else if (e.key === 'Escape') {
        handleGoBack()
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [currentStage, totalStages])

  if (error) {
    return (
      <div className="h-screen w-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center p-8 bg-white rounded-2xl shadow-xl border border-slate-200 max-w-md"
        >
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-slate-900 mb-2">Story Not Available</h2>
          <p className="text-slate-600 mb-6">{error}</p>
          <div className="flex gap-3 justify-center">
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-all duration-200 font-medium"
            >
              Try Again
            </button>
            <button
              onClick={handleGoBack}
              className="px-6 py-3 bg-slate-500 text-white rounded-xl hover:bg-slate-600 transition-all duration-200 font-medium"
            >
              Go Back
            </button>
          </div>
        </motion.div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="h-screen w-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center p-8 bg-white rounded-2xl shadow-xl border border-slate-200"
        >
          <Loader2 className="w-12 h-12 text-blue-500 mx-auto mb-4 animate-spin" />
          <h2 className="text-xl font-semibold text-slate-900 mb-2">Loading Story</h2>
          <p className="text-slate-600">Preparing your reading experience...</p>
        </motion.div>
      </div>
    )
  }

  if (!storyData) {
    return (
      <div className="h-screen w-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center p-8 bg-white rounded-2xl shadow-xl border border-slate-200 max-w-md"
        >
          <BookOpen className="w-16 h-16 text-slate-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-slate-900 mb-2">Story Not Found</h2>
          <p className="text-slate-600 mb-6">
            The story you're looking for doesn't exist or has been removed.
          </p>
          <button
            onClick={handleGoBack}
            className="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-all duration-200 font-medium"
          >
            Back to Stories
          </button>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="h-screen w-screen flex flex-col overflow-hidden bg-gradient-to-br from-slate-50 to-slate-100">
      {/* FIXED TOP INFO BAR */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/95 backdrop-blur-sm border-b border-slate-200 p-4 shrink-0 shadow-sm relative z-10"
      >
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <button
                onClick={handleGoBack}
                className="flex items-center gap-2 px-4 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all duration-200 font-medium"
              >
                <ArrowLeft className="w-4 h-4" />
                Back
              </button>
              <div>
                <h1 className="text-lg font-bold text-slate-900">
                  Story #{storyData.story_id.slice(-8)}
                </h1>
                <div className="flex items-center gap-3 text-sm text-slate-600">
                  <span>Chapter {currentStage} of {totalStages}</span>
                  <span>•</span>
                  <span>{Math.round((currentStage / totalStages) * 100)}% Complete</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <span className={cn(
                "px-3 py-1.5 text-sm font-medium rounded-full border",
                storyData.status === 'completed' ? 'text-emerald-600 bg-emerald-50 border-emerald-200' :
                storyData.status === 'processing' ? 'text-amber-600 bg-amber-50 border-amber-200' :
                'text-slate-600 bg-slate-50 border-slate-200'
              )}>
                {storyData.status}
              </span>
            </div>
          </div>
          
          {/* Enhanced Progress Bar */}
          <div className="relative">
            <div className="w-full bg-slate-200 rounded-full h-3 overflow-hidden">
              <motion.div
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(currentStage / totalStages) * 100}%` }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              />
            </div>
            <div className="flex justify-between mt-2">
              {Array.from({ length: Math.min(totalStages, 10) }, (_, i) => {
                const stage = Math.floor((i / 9) * (totalStages - 1)) + 1
                return (
                  <button
                    key={stage}
                    onClick={() => handleStageChange(stage)}
                    className={cn(
                      "w-2 h-2 rounded-full transition-all duration-200",
                      currentStage >= stage
                        ? "bg-blue-500 scale-125"
                        : "bg-slate-300 hover:bg-slate-400"
                    )}
                  />
                )
              })}
            </div>
          </div>
        </div>
      </motion.div>

      {/* MIDDLE CONTENT AREA - ONLY THIS CHANGES */}
      <div className="flex-1 min-h-0 overflow-hidden relative">
        <AnimatePresence mode="wait">
          <StagePage key={currentStage} />
        </AnimatePresence>
      </div>

      {/* FIXED BOTTOM NAVIGATION */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/95 backdrop-blur-sm border-t border-slate-200 p-4 shrink-0 shadow-lg relative z-10"
      >
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between">
            <button
              onClick={handlePreviousStage}
              disabled={currentStage === 1}
              className="flex items-center gap-2 px-6 py-3 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-slate-100"
            >
              <ChevronLeft className="w-5 h-5" />
              Previous
            </button>

            <div className="flex items-center gap-2">
              {Array.from({ length: totalStages }, (_, i) => i + 1).map((stage) => (
                <button
                  key={stage}
                  onClick={() => handleStageChange(stage)}
                  className={cn(
                    "w-10 h-10 rounded-xl text-sm font-bold transition-all duration-200",
                    currentStage === stage
                      ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg scale-110"
                      : "bg-slate-100 text-slate-700 hover:bg-slate-200 hover:scale-105"
                  )}
                >
                  {stage}
                </button>
              ))}
            </div>

            <button
              onClick={handleNextStage}
              disabled={currentStage === totalStages}
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-xl transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:from-slate-300 disabled:to-slate-300"
            >
              Next
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default StoryLayout

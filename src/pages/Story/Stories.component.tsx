import React from 'react'
import { motion } from 'framer-motion'
import {
  RefreshCw,
  BookOpen,
  ChevronRight,
  AlertCircle,
  Play,
  ChevronLeft,
  Eye,
  Calendar,
  Volume2,
  Pause
} from 'lucide-react'
import MainLayout from '../../components/layout/MainLayout'
import { cn } from '../../utils/cn'
import { StoryListItem } from '../../services/story/storyService'

interface StoriesComponentProps {
  stories: StoryListItem[]
  isLoading: boolean
  error: string | null
  currentPage: number
  totalPages: number
  totalStories: number
  onStorySelect: (storyId: string) => void
  onPageChange: (page: number) => void
  onRetry: () => void
  onRefresh: () => void
}

const StoriesComponent: React.FC<StoriesComponentProps> = ({
  stories,
  isLoading,
  error,
  currentPage,
  totalPages,
  totalStories,
  onStorySelect,
  onPageChange,
  onRetry,
  onRefresh
}) => {
  const [playingAudio, setPlayingAudio] = React.useState<string | null>(null)
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'processing':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const handleAudioPlay = (storyId: string, audioUrl: string, event: React.MouseEvent) => {
    event.stopPropagation() // Prevent story selection when clicking audio controls

    if (playingAudio === storyId) {
      // Pause current audio
      const audioElement = document.getElementById(`audio-${storyId}`) as HTMLAudioElement
      if (audioElement) {
        audioElement.pause()
      }
      setPlayingAudio(null)
    } else {
      // Stop any currently playing audio
      if (playingAudio) {
        const currentAudio = document.getElementById(`audio-${playingAudio}`) as HTMLAudioElement
        if (currentAudio) {
          currentAudio.pause()
        }
      }

      // Play new audio
      const audioElement = document.getElementById(`audio-${storyId}`) as HTMLAudioElement
      if (audioElement) {
        audioElement.play()
      }
      setPlayingAudio(storyId)
    }
  }

  const handleAudioEnded = (storyId: string) => {
    if (playingAudio === storyId) {
      setPlayingAudio(null)
    }
  }

  if (error) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold text-foreground mb-2">Error Loading Stories</h2>
          <p className="text-muted-foreground mb-6 max-w-md">{error}</p>
          <button
            onClick={onRetry}
            className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">My Stories</h1>
            <p className="text-muted-foreground">
              {totalStories > 0 ? `${totalStories} stories found` : 'No stories yet'}
            </p>
          </div>
          <button
            onClick={onRefresh}
            disabled={isLoading}
            className="flex items-center gap-2 px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
            Refresh
          </button>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-card border border-border rounded-xl p-6 shadow-sm">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-3"></div>
                  <div className="h-3 bg-muted rounded w-1/2 mb-4"></div>
                  <div className="h-20 bg-muted rounded mb-4"></div>
                  <div className="flex justify-between items-center">
                    <div className="h-3 bg-muted rounded w-1/4"></div>
                    <div className="h-6 bg-muted rounded w-16"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Stories Grid */}
        {!isLoading && stories.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {stories.map((story, index) => (
              <motion.div
                key={story.story_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-card border border-border rounded-xl p-6 shadow-sm hover:shadow-md transition-all cursor-pointer group"
                onClick={() => onStorySelect(story.story_id)}
              >
                {/* Story Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <BookOpen className="w-5 h-5 text-primary" />
                    <span className="text-sm font-medium text-muted-foreground">
                      Story #{story.story_id.slice(-6)}
                    </span>
                  </div>
                  <span className={cn(
                    "px-2 py-1 text-xs font-medium rounded-full border",
                    getStatusColor(story.status)
                  )}>
                    {story.status}
                  </span>
                </div>

                {/* Story Content Preview - Comic Book Style */}
                <div className="mb-4">
                  {story.media_url ? (
                    <div
                      className="w-full h-32 bg-muted rounded-lg overflow-hidden relative"
                      style={{
                        backgroundImage: `url(${story.media_url})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    >
                      {/* Text overlay with comic book styling */}
                      <div className="absolute inset-0 bg-black/40 flex items-center justify-center p-3">
                        <p className="text-white text-xs font-medium text-center line-clamp-3 drop-shadow-lg">
                          {story.first_step_script || 'No script available'}
                        </p>
                      </div>

                      {/* Audio player overlay */}
                      {story.audio_url && (
                        <div className="absolute top-2 right-2">
                          <button
                            onClick={(e) => handleAudioPlay(story.story_id, story.audio_url!, e)}
                            className="flex items-center justify-center w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white transition-colors"
                          >
                            {playingAudio === story.story_id ? (
                              <Pause className="w-4 h-4 text-gray-700" />
                            ) : (
                              <Play className="w-4 h-4 text-gray-700 ml-0.5" />
                            )}
                          </button>
                          <audio
                            id={`audio-${story.story_id}`}
                            src={story.audio_url}
                            onEnded={() => handleAudioEnded(story.story_id)}
                            preload="none"
                          />
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="w-full h-32 bg-muted rounded-lg flex items-center justify-center relative">
                      <div className="text-center p-3">
                        <BookOpen className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {story.first_step_script || 'No script available'}
                        </p>
                      </div>

                      {/* Audio player for stories without background image */}
                      {story.audio_url && (
                        <div className="absolute top-2 right-2">
                          <button
                            onClick={(e) => handleAudioPlay(story.story_id, story.audio_url!, e)}
                            className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-full shadow-lg hover:bg-primary/90 transition-colors"
                          >
                            {playingAudio === story.story_id ? (
                              <Pause className="w-4 h-4" />
                            ) : (
                              <Play className="w-4 h-4 ml-0.5" />
                            )}
                          </button>
                          <audio
                            id={`audio-${story.story_id}`}
                            src={story.audio_url}
                            onEnded={() => handleAudioEnded(story.story_id)}
                            preload="none"
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Story Stats */}
                <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      <span>{story.completed_steps}/{story.total_steps}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(story.created_at)}</span>
                    </div>
                    {story.audio_url && (
                      <div className="flex items-center gap-1">
                        <Volume2 className="w-4 h-4" />
                        <span>Audio</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between text-xs text-muted-foreground mb-1">
                    <span>Progress</span>
                    <span>{Math.round((story.completed_steps / story.total_steps) * 100)}%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all"
                      style={{ width: `${(story.completed_steps / story.total_steps) * 100}%` }}
                    />
                  </div>
                </div>

                {/* Action */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Play className="w-4 h-4" />
                    <span>Continue Reading</span>
                  </div>
                  <ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Empty State */}
        {!isLoading && stories.length === 0 && (
          <div className="flex flex-col items-center justify-center min-h-[40vh] text-center">
            <BookOpen className="w-16 h-16 text-muted-foreground mb-4" />
            <h2 className="text-2xl font-bold text-foreground mb-2">No Stories Yet</h2>
            <p className="text-muted-foreground mb-6 max-w-md">
              Start creating your first story by recording audio in the Begin Learning section.
            </p>
          </div>
        )}

        {/* Pagination */}
        {!isLoading && totalPages > 1 && (
          <div className="flex items-center justify-center gap-2 mt-8">
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="flex items-center gap-1 px-3 py-2 text-sm bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4" />
              Previous
            </button>
            
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1
                return (
                  <button
                    key={page}
                    onClick={() => onPageChange(page)}
                    className={cn(
                      "px-3 py-2 text-sm rounded-lg transition-colors",
                      currentPage === page
                        ? "bg-primary text-primary-foreground"
                        : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                    )}
                  >
                    {page}
                  </button>
                )
              })}
            </div>

            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="flex items-center gap-1 px-3 py-2 text-sm bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    </MainLayout>
  )
}

export default StoriesComponent

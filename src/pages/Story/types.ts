import { StoryData, StoryGenerationResponse } from '../../services/story/storyService'

// Component props interfaces
export interface StoryComponentProps {
  // Recording state
  isRecording: boolean
  audioBlob: Blob | null
  recordingTime: number
  
  // Story generation state
  isGenerating: boolean
  generationError: string | null
  storyId: string | null
  
  // Story data state
  storyData: StoryData | null
  currentStage: number
  isLoadingStory: boolean
  storyError: string | null
  
  // Handlers
  onStartRecording: () => void
  onStopRecording: () => void
  onGenerateStory: () => void
  onStageChange: (stage: number) => void
  onClearError: () => void
  onReset: () => void
}

// Recording states
export type RecordingState = 'idle' | 'recording' | 'stopped' | 'generating'

// Story generation states
export type StoryGenerationState = 'idle' | 'uploading' | 'processing' | 'completed' | 'error'

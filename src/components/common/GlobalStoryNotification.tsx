import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '../../store/hooks'
import { markStoryAsVisited, dismissStoryNotification, undismissStoryNotification } from '../../store/slices/storySlice'

interface GlobalStoryNotificationProps {
  position?: 'top-right' | 'bottom-right' | 'bottom-left' | 'top-left'
}

/**
 * Global Story Notification Component
 * Shows persistent notification across the entire app when new stories are available
 * Persists in localStorage until user visits the story
 */
const GlobalStoryNotification: React.FC<GlobalStoryNotificationProps> = ({
  position = 'top-right'
}) => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const { stories, dismissedNotifications } = useAppSelector((state) => (state.story as any) || { stories: {}, dismissedNotifications: [] })

  // Get unvisited and non-dismissed stories
  const unvisitedStories = Object.values(stories || {}).filter((story: any) =>
    !story.visited && !dismissedNotifications.includes(story.storyId)
  )
  
  const hasUnvisitedStories = unvisitedStories.length > 0

  // Get the most recent unvisited story
  const mostRecentStory = unvisitedStories.sort((a: any, b: any) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )[0] as any

  const handleDismissNotification = () => {
    if (mostRecentStory) {
      // Add to dismissed list (temporary hide)
      dispatch(dismissStoryNotification(mostRecentStory.storyId))
    }
  }

  const handleVisitStory = () => {
    if (mostRecentStory) {
      // Mark as visited in Redux
      dispatch(markStoryAsVisited(mostRecentStory.storyId))

      // Remove from dismissed list since it's now visited
      dispatch(undismissStoryNotification(mostRecentStory.storyId))

      // Navigate to story
      navigate(`/story/${mostRecentStory.storyId}`)
    }
  }

  if (!hasUnvisitedStories) {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, x: 300, scale: 0.8 }}
        animate={{ opacity: 1, x: 0, scale: 1 }}
        exit={{ opacity: 0, x: 300, scale: 0.8 }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
        className="fixed z-[9999] pointer-events-none"
        style={{
          top: position.includes('top') ? '1rem' : 'auto',
          bottom: position.includes('bottom') ? '1rem' : 'auto',
          left: position.includes('left') ? '1rem' : 'auto',
          right: position.includes('right') ? '1rem' : 'auto',
        }}
      >
        <div className="pointer-events-auto">
          <div className="min-w-[320px] max-w-[400px] bg-white dark:bg-gray-800 border border-purple-200 dark:border-purple-700 shadow-xl hover:shadow-2xl rounded-lg p-4 flex items-center gap-3 transition-all duration-200">
            {/* Notification Icon */}
            <motion.div
              animate={{
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-purple-600 dark:text-purple-400"
            >
              🎉
            </motion.div>

            {/* Content */}
            <div className="flex-1">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                New Story Ready!
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Your personalized story has been generated
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <motion.button
                onClick={handleVisitStory}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-3 py-1.5 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 transition-colors"
              >
                Visit
              </motion.button>

              <motion.button
                onClick={handleDismissNotification}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                ✕
              </motion.button>
            </div>
          </div>
        </div>

        {/* Pulse indicator */}
        <motion.div
          className="absolute inset-0 rounded-lg bg-purple-400 opacity-20 pointer-events-none"
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0.2, 0.1, 0.2]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Story count badge */}
        {unvisitedStories.length > 1 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold pointer-events-none"
          >
            {unvisitedStories.length}
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  )
}

export default GlobalStoryNotification

import React, { useState } from 'react'
import * as Dialog from '@radix-ui/react-dialog'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import {
  Trophy,
  Star,
  Sparkles,
  ArrowRight,
  RotateCcw,
  BookOpen,
  CheckCircle,
  X
} from 'lucide-react'
import { cn } from '../../utils/cn'
import TaskSetStoriesModal from '../story/TaskSetStoriesModal'

interface CompletionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onRetryTasks: () => void
  onMoveToStories: () => void
  taskSetId?: string
  taskSetTitle?: string
  totalTasks?: number
  totalScore?: number
  userScore?: number
}

/**
 * Beautiful completion modal shown when all tasks in a task set are completed
 */
const CompletionModal: React.FC<CompletionModalProps> = ({
  open,
  onOpenChange,
  onRetryTasks,
  onMoveToStories,
  taskSetId,
  taskSetTitle = "Task Set",
  totalTasks = 0,
  totalScore = 0,
  userScore = 0
}) => {
  const navigate = useNavigate()
  const [showStoriesModal, setShowStoriesModal] = useState(false)
  const completionPercentage = totalScore > 0 ? Math.round((userScore / totalScore) * 100) : 0

  const handleMoveToStoriesClick = () => {
    if (taskSetId) {
      setShowStoriesModal(true)
    } else {
      // Fallback to original behavior
      onMoveToStories()
    }
  }

  const handleStorySelect = (storyId: string) => {
    if (taskSetId) {
      navigate(`/tasks/${taskSetId}/storyitem/${storyId}?stage=1`)
    } else {
      // Fallback to old routing if no taskSetId
      navigate(`/story/${storyId}?stage=1`)
    }
    onOpenChange(false)
  }

  const getPerformanceMessage = () => {
    if (completionPercentage >= 90) return "Outstanding performance! 🌟"
    if (completionPercentage >= 80) return "Excellent work! 🎉"
    if (completionPercentage >= 70) return "Great job! 👏"
    if (completionPercentage >= 60) return "Good effort! 👍"
    return "Keep practicing! 💪"
  }

  const getPerformanceColor = () => {
    if (completionPercentage >= 90) return "text-yellow-600"
    if (completionPercentage >= 80) return "text-green-600"
    if (completionPercentage >= 70) return "text-blue-600"
    if (completionPercentage >= 60) return "text-purple-600"
    return "text-gray-600"
  }

  return (
    <>
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/60 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 z-50" />
        <Dialog.Content className="fixed left-[50%] top-[50%] z-50 w-full max-w-md translate-x-[-50%] translate-y-[-50%] duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-slate-900 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 overflow-hidden"
          >
            {/* Close button */}
            <Dialog.Close asChild>
              <button className="absolute right-4 top-4 p-2 rounded-full hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors z-10">
                <X className="h-4 w-4 text-slate-500" />
              </button>
            </Dialog.Close>

            {/* Header with celebration animation */}
            <div className="relative bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 p-8 text-center text-white overflow-hidden">
              {/* Animated background elements */}
              <div className="absolute inset-0">
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute"
                    initial={{ 
                      opacity: 0, 
                      scale: 0,
                      x: Math.random() * 300,
                      y: Math.random() * 200
                    }}
                    animate={{ 
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0],
                      rotate: [0, 180, 360]
                    }}
                    transition={{
                      duration: 2,
                      delay: i * 0.2,
                      repeat: Infinity,
                      repeatDelay: 3
                    }}
                  >
                    <Sparkles className="h-4 w-4 text-yellow-300" />
                  </motion.div>
                ))}
              </div>

              {/* Main trophy icon */}
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ 
                  type: "spring", 
                  stiffness: 200, 
                  damping: 15,
                  delay: 0.2 
                }}
                className="relative z-10 mb-4"
              >
                <div className="inline-flex p-4 bg-white/20 rounded-full">
                  <Trophy className="h-12 w-12 text-yellow-300" />
                </div>
              </motion.div>

              {/* Title */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="relative z-10"
              >
                <h1 className="text-2xl font-bold mb-2">
                  Congratulations! 🎉
                </h1>
                <p className="text-white/90 text-sm">
                  You've completed all tasks!
                </p>
              </motion.div>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Task Set Info */}
              <div className="text-center">
                <h2 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                  {taskSetTitle}
                </h2>
                <p className={cn("text-sm font-medium", getPerformanceColor())}>
                  {getPerformanceMessage()}
                </p>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="text-lg font-bold text-slate-900 dark:text-white">
                    {totalTasks}
                  </div>
                  <div className="text-xs text-slate-600 dark:text-slate-400">
                    Tasks
                  </div>
                </div>
                
                <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Star className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div className="text-lg font-bold text-slate-900 dark:text-white">
                    {userScore}/{totalScore}
                  </div>
                  <div className="text-xs text-slate-600 dark:text-slate-400">
                    Score
                  </div>
                </div>
                
                <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Trophy className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="text-lg font-bold text-slate-900 dark:text-white">
                    {completionPercentage}%
                  </div>
                  <div className="text-xs text-slate-600 dark:text-slate-400">
                    Accuracy
                  </div>
                </div>
              </div>

              {/* Action buttons */}
              <div className="space-y-3">
                <button
                  onClick={handleMoveToStoriesClick}
                  className="w-full flex items-center justify-center gap-3 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02]"
                >
                  <BookOpen className="h-4 w-4" />
                  Move to Stories
                  <ArrowRight className="h-4 w-4" />
                </button>

                <button
                  onClick={onRetryTasks}
                  className="w-full flex items-center justify-center gap-3 px-6 py-3 bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 text-slate-900 dark:text-white font-medium rounded-lg transition-all duration-200"
                >
                  <RotateCcw className="h-4 w-4" />
                  Retry Tasks
                </button>
              </div>
            </div>
          </motion.div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>

    {/* Stories Selection Modal */}
    {taskSetId && (
      <TaskSetStoriesModal
        open={showStoriesModal}
        onOpenChange={setShowStoriesModal}
        taskSetId={taskSetId}
        onStorySelect={handleStorySelect}
      />
    )}
  </>
  )
}

export default CompletionModal
